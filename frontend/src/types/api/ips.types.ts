// IPS (Ideal Performance State) related types

export interface IPSRecord {
  id: string;
  coacheeId: string;
  dateTime: string; // ISO string
  competitionDateTime: string; // ISO string
  competitionName: string;
  performanceScore: number; // 0-10
  arousalScore: number; // 0-10
  concentration?: string; // Concentration notes/description
  confidence?: string; // Confidence notes/description
  createdAt: string; // ISO string
  updatedAt: string; // ISO string
  coachee: {
    id: string;
    firstName: string | null;
    lastName: string | null;
    email: string;
  };
}

export interface CreateIPSRecordRequest {
  dateTime: string; // ISO string
  competitionDateTime: string; // ISO string
  competitionName: string;
  performanceScore: number; // 0-10
  arousalScore: number; // 0-10
  concentration?: string; // Concentration notes/description
  confidence?: string; // Confidence notes/description
}

export interface UpdateIPSRecordRequest {
  dateTime?: string; // ISO string
  competitionDateTime?: string; // ISO string
  competitionName?: string;
  performanceScore?: number; // 0-10
  arousalScore?: number; // 0-10
  concentration?: string; // Concentration notes/description
  confidence?: string; // Confidence notes/description
}

export interface GetIPSRecordsRequest {
  coacheeId?: string;
  startDate?: string; // ISO string
  endDate?: string; // ISO string
  limit?: number;
  offset?: number;
}

export interface GetIPSRecordsResponse {
  ipsRecords: IPSRecord[];
  total: number;
  hasMore: boolean;
}

export interface IPSTrendData {
  id: string;
  competitionDateTime: string; // ISO string
  competitionName: string;
  performanceScore: number;
  arousalScore: number;
}

export interface GetIPSTrendsRequest {
  coacheeId: string;
  startDate?: string; // ISO string
  endDate?: string; // ISO string
}

export type GetIPSTrendsResponse = IPSTrendData[];

// Form data types for frontend components
export interface IPSFormData {
  dateTime: Date;
  competitionDateTime: Date;
  competitionName: string;
  performanceScore: number;
  arousalScore: number;
}

// Chart data types for visualization
export interface IPSChartDataPoint {
  date: string;
  competitionName: string;
  performance: number;
  arousal: number;
  timestamp: number; // For sorting
}

export interface IPSChartData {
  dataPoints: IPSChartDataPoint[];
  performanceAverage: number;
  arousalAverage: number;
  totalRecords: number;
}
